# Interactive VM Deployment Script
# This script provides a simple form-like interface for VM deployment

param(
    [string]$VmName = "CCH9APPVMCT",
    [int]$VmCount = 1,
    [string]$ResourceGroup = "MAH9-SUP-CCH9-VMC",
    [string]$VmSize = "Standard_D4s_v3",
    [string]$Location = "East US 2",
    [string]$AdminUsername = "maoperator",
    [string]$AdminPassword = "M1t1g@t0r2025",
    [string]$TeamProject = "MAH9-SCP-CCH9GI",
    [string]$DeploymentGroup = "MAH9-SCP-CCH9-DGR-DEV",
    [string]$AzureDevOpsPat = "4znAQCp60VTfBLNJ1BRLFtP9S0dC7a9GJAsDHgGWbvqHMzLTZacPJQQJ99BGACAAAAA71N4WAAASAZDO2Yr7"
)

Clear-Host

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Azure VM Deployment - Interactive" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to get user input with default value
function Get-UserInput {
    param(
        [string]$Prompt,
        [string]$DefaultValue
    )
    
    $input = Read-Host "$Prompt (default: $DefaultValue)"
    if ([string]::IsNullOrWhiteSpace($input)) {
        return $DefaultValue
    }
    return $input
}

# Function to show current configuration
function Show-Configuration {
    Write-Host "Current Configuration:" -ForegroundColor Yellow
    Write-Host "- VM Name: $VmName" -ForegroundColor White
    Write-Host "- VM Count: $VmCount" -ForegroundColor White
    Write-Host "- Resource Group: $ResourceGroup" -ForegroundColor White
    Write-Host "- VM Size: $VmSize" -ForegroundColor White
    Write-Host "- Location: $Location" -ForegroundColor White
    Write-Host "- Admin Username: $AdminUsername" -ForegroundColor White
    Write-Host "- Team Project: $TeamProject" -ForegroundColor White
    Write-Host "- Deployment Group: $DeploymentGroup" -ForegroundColor White
    Write-Host ""
}

# Show initial configuration
Show-Configuration

# Ask if user wants to modify settings
$modify = Read-Host "Do you want to modify any settings? (y/N)"
if ($modify -eq 'y' -or $modify -eq 'Y') {
    Write-Host ""
    Write-Host "Enter new values (press Enter to keep current value):" -ForegroundColor Green
    
    $VmName = Get-UserInput "VM Name" $VmName
    $VmCount = [int](Get-UserInput "VM Count" $VmCount)
    $ResourceGroup = Get-UserInput "Resource Group" $ResourceGroup
    $VmSize = Get-UserInput "VM Size" $VmSize
    $Location = Get-UserInput "Location" $Location
    $AdminUsername = Get-UserInput "Admin Username" $AdminUsername
    $AdminPassword = Get-UserInput "Admin Password" $AdminPassword
    $TeamProject = Get-UserInput "Team Project" $TeamProject
    $DeploymentGroup = Get-UserInput "Deployment Group" $DeploymentGroup
    
    Write-Host ""
    Write-Host "Updated Configuration:" -ForegroundColor Green
    Show-Configuration
}

# Confirm deployment
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "    READY TO DEPLOY: $VmName" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
Write-Host ""
Write-Host "This will create real Azure resources and may incur costs." -ForegroundColor Red
$confirm = Read-Host "Do you want to proceed with deployment? (y/N)"

if ($confirm -ne 'y' -and $confirm -ne 'Y') {
    Write-Host "Deployment cancelled by user" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "    STARTING DEPLOYMENT: $VmName" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

try {
    # Create terraform.tfvars
    Write-Host "Creating terraform.tfvars..." -ForegroundColor Cyan
    
    $tfvarsContent = @"
vm_name = "$VmName"
vm_count = $VmCount
vm_resourcegroup = "$ResourceGroup"
vm_size = "$VmSize"
vm_sku = "2022-datacenter"
vm_location = "$Location"
vm_admin_username = "$AdminUsername"
vm_admin_password = "$AdminPassword"
azure_devops_organization = "https://dev.azure.com/MAHealth"
azure_devops_teamproject = "$TeamProject"
azure_devops_deploymentgroup = "$DeploymentGroup"
azure_devops_pat = "$AzureDevOpsPat"
azure_devops_agentfolder = "c:/Agent"
"@

    $tfvarsContent | Out-File -FilePath "terraform.tfvars" -Encoding UTF8
    Write-Host "terraform.tfvars created successfully!" -ForegroundColor Green
    Write-Host ""

    # Check if terraform is available
    if (!(Get-Command terraform -ErrorAction SilentlyContinue)) {
        throw "Terraform is not installed or not in PATH. Please install Terraform and try again."
    }

    # Initialize Terraform
    Write-Host "Initializing Terraform..." -ForegroundColor Cyan
    terraform init
    if ($LASTEXITCODE -ne 0) { throw "Terraform init failed" }
    Write-Host "Terraform initialized successfully!" -ForegroundColor Green
    Write-Host ""

    # Validate configuration
    Write-Host "Validating configuration..." -ForegroundColor Cyan
    terraform validate
    if ($LASTEXITCODE -ne 0) { throw "Terraform validation failed" }
    Write-Host "Configuration validated successfully!" -ForegroundColor Green
    Write-Host ""

    # Show plan
    Write-Host "Showing deployment plan..." -ForegroundColor Cyan
    terraform plan -var-file="terraform.tfvars"
    if ($LASTEXITCODE -ne 0) { throw "Terraform plan failed" }
    Write-Host ""

    # Final confirmation
    Write-Host "========================================" -ForegroundColor Yellow
    Write-Host "    FINAL CONFIRMATION" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Yellow
    $finalConfirm = Read-Host "Proceed with deployment? (y/N)"
    
    if ($finalConfirm -ne 'y' -and $finalConfirm -ne 'Y') {
        Write-Host "Deployment cancelled" -ForegroundColor Yellow
        Read-Host "Press Enter to exit"
        exit
    }

    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "    DEPLOYING VM: $VmName" -ForegroundColor Green
    Write-Host "    This may take 10-15 minutes..." -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""

    # Apply configuration
    terraform apply -var-file="terraform.tfvars" -auto-approve
    if ($LASTEXITCODE -ne 0) { throw "Terraform apply failed" }

    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "    DEPLOYMENT COMPLETED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "    VM: $VmName" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Your VM has been deployed successfully!" -ForegroundColor Green
    Write-Host "You can now access it using the credentials provided." -ForegroundColor Green
    Write-Host ""

    # Show deployment details
    Write-Host "Deployment details:" -ForegroundColor Cyan
    terraform show

} catch {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "    DEPLOYMENT FAILED!" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please check the error messages above and try again." -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to exit"
