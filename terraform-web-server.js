// Node.js Web Server for Terraform VM Deployment
// This server provides a web interface that can directly execute Terraform

const express = require('express');
const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());
app.use(express.static('.'));

// Store deployment status
let deploymentStatus = {};

// Serve the main form
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'vm-deployment-form-auto.html'));
});

// API endpoint to deploy VM
app.post('/api/deploy', async (req, res) => {
    const deploymentId = Date.now().toString();
    const {
        action,
        vmName,
        vmCount,
        resourceGroup,
        vmSize,
        vmSku,
        location,
        adminUsername,
        adminPassword,
        azureDevOpsOrg,
        teamProject,
        deploymentGroup,
        azureDevOpsPat,
        agentFolder
    } = req.body;

    console.log(`Starting deployment ${deploymentId}: ${action} for ${vmName}`);

    // Initialize deployment status
    deploymentStatus[deploymentId] = {
        status: 'starting',
        message: 'Initializing deployment...',
        logs: [],
        startTime: new Date(),
        action: action,
        vmName: vmName
    };

    // Send immediate response with deployment ID
    res.json({
        success: true,
        deploymentId: deploymentId,
        message: 'Deployment started',
        status: 'starting'
    });

    // Execute deployment asynchronously
    executeDeployment(deploymentId, {
        action,
        vmName,
        vmCount: vmCount || 1,
        resourceGroup,
        vmSize: vmSize || 'Standard_D4s_v3',
        vmSku: vmSku || '2022-datacenter',
        location: location || 'East US 2',
        adminUsername,
        adminPassword,
        azureDevOpsOrg,
        teamProject,
        deploymentGroup,
        azureDevOpsPat,
        agentFolder: agentFolder || 'c:/Agent'
    });
});

// API endpoint to check deployment status
app.get('/api/status/:deploymentId', (req, res) => {
    const deploymentId = req.params.deploymentId;
    const status = deploymentStatus[deploymentId];
    
    if (!status) {
        return res.status(404).json({ error: 'Deployment not found' });
    }
    
    res.json(status);
});

// API endpoint to get all deployments
app.get('/api/deployments', (req, res) => {
    const deployments = Object.keys(deploymentStatus).map(id => ({
        id,
        ...deploymentStatus[id]
    }));
    res.json(deployments);
});

async function executeDeployment(deploymentId, params) {
    try {
        updateDeploymentStatus(deploymentId, 'running', 'Creating terraform.tfvars...');

        // Create terraform.tfvars file
        const tfvarsContent = `
vm_name = "${params.vmName}"
vm_count = ${params.vmCount}
vm_resourcegroup = "${params.resourceGroup}"
vm_size = "${params.vmSize}"
vm_sku = "${params.vmSku}"
vm_location = "${params.location}"
vm_admin_username = "${params.adminUsername}"
vm_admin_password = "${params.adminPassword}"
azure_devops_organization = "${params.azureDevOpsOrg}"
azure_devops_teamproject = "${params.teamProject}"
azure_devops_deploymentgroup = "${params.deploymentGroup}"
azure_devops_pat = "${params.azureDevOpsPat}"
azure_devops_agentfolder = "${params.agentFolder}"
`;

        fs.writeFileSync('terraform.tfvars', tfvarsContent);
        updateDeploymentStatus(deploymentId, 'running', 'terraform.tfvars created successfully');

        // Initialize Terraform if needed
        updateDeploymentStatus(deploymentId, 'running', 'Initializing Terraform...');
        await executeCommand('terraform init', deploymentId);

        // Validate Terraform configuration
        updateDeploymentStatus(deploymentId, 'running', 'Validating Terraform configuration...');
        await executeCommand('terraform validate', deploymentId);

        if (params.action === 'plan') {
            // Execute terraform plan
            updateDeploymentStatus(deploymentId, 'running', 'Running Terraform plan...');
            await executeCommand('terraform plan -var-file=terraform.tfvars', deploymentId);
            updateDeploymentStatus(deploymentId, 'completed', 'Terraform plan completed successfully');
        } else if (params.action === 'apply') {
            // Execute terraform plan first
            updateDeploymentStatus(deploymentId, 'running', 'Running Terraform plan...');
            await executeCommand('terraform plan -var-file=terraform.tfvars', deploymentId);

            // Execute terraform apply
            updateDeploymentStatus(deploymentId, 'running', 'Applying Terraform configuration...');
            await executeCommand('terraform apply -var-file=terraform.tfvars -auto-approve', deploymentId);
            updateDeploymentStatus(deploymentId, 'completed', `VM ${params.vmName} deployed successfully!`);
        } else if (params.action === 'destroy') {
            // Execute terraform destroy
            updateDeploymentStatus(deploymentId, 'running', 'Destroying Terraform resources...');
            await executeCommand('terraform destroy -var-file=terraform.tfvars -auto-approve', deploymentId);
            updateDeploymentStatus(deploymentId, 'completed', 'Infrastructure destroyed successfully');
        }

    } catch (error) {
        console.error(`Deployment ${deploymentId} failed:`, error);
        updateDeploymentStatus(deploymentId, 'failed', `Deployment failed: ${error.message}`);
    }
}

function executeCommand(command, deploymentId) {
    return new Promise((resolve, reject) => {
        console.log(`Executing: ${command}`);
        updateDeploymentStatus(deploymentId, 'running', `Executing: ${command}`);

        const process = exec(command, { cwd: __dirname }, (error, stdout, stderr) => {
            if (error) {
                console.error(`Error executing ${command}:`, error);
                addDeploymentLog(deploymentId, `ERROR: ${error.message}`);
                reject(error);
                return;
            }

            if (stderr) {
                console.error(`stderr: ${stderr}`);
                addDeploymentLog(deploymentId, `STDERR: ${stderr}`);
            }

            console.log(`stdout: ${stdout}`);
            addDeploymentLog(deploymentId, stdout);
            resolve(stdout);
        });

        // Capture real-time output
        process.stdout.on('data', (data) => {
            console.log(data.toString());
            addDeploymentLog(deploymentId, data.toString());
        });

        process.stderr.on('data', (data) => {
            console.error(data.toString());
            addDeploymentLog(deploymentId, `ERROR: ${data.toString()}`);
        });
    });
}

function updateDeploymentStatus(deploymentId, status, message) {
    if (deploymentStatus[deploymentId]) {
        deploymentStatus[deploymentId].status = status;
        deploymentStatus[deploymentId].message = message;
        deploymentStatus[deploymentId].lastUpdate = new Date();
        
        if (status === 'completed' || status === 'failed') {
            deploymentStatus[deploymentId].endTime = new Date();
            const duration = deploymentStatus[deploymentId].endTime - deploymentStatus[deploymentId].startTime;
            deploymentStatus[deploymentId].duration = Math.round(duration / 1000); // seconds
        }
    }
    console.log(`Deployment ${deploymentId}: ${status} - ${message}`);
}

function addDeploymentLog(deploymentId, logEntry) {
    if (deploymentStatus[deploymentId]) {
        deploymentStatus[deploymentId].logs.push({
            timestamp: new Date(),
            message: logEntry.trim()
        });
        
        // Keep only last 100 log entries to prevent memory issues
        if (deploymentStatus[deploymentId].logs.length > 100) {
            deploymentStatus[deploymentId].logs = deploymentStatus[deploymentId].logs.slice(-100);
        }
    }
}

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'healthy', 
        timestamp: new Date(),
        activeDeployments: Object.keys(deploymentStatus).length
    });
});

// Start the server
app.listen(PORT, () => {
    console.log(`🚀 Terraform Web Server running on http://localhost:${PORT}`);
    console.log(`📁 Working directory: ${__dirname}`);
    console.log(`⚡ Ready to deploy VMs automatically!`);
    console.log('');
    console.log('Prerequisites:');
    console.log('- Node.js and npm installed');
    console.log('- Terraform installed and in PATH');
    console.log('- Azure CLI authenticated (az login)');
    console.log('- Run: npm install express cors');
    console.log('');
    console.log('Open your browser and go to: http://localhost:3000');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down Terraform Web Server...');
    process.exit(0);
});
