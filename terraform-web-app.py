#!/usr/bin/env python3
"""
Terraform Web Application for VM Deployment
This creates a web server that can execute Terraform deployments directly
"""

import os
import subprocess
import threading
import time
from datetime import datetime
from flask import Flask, render_template_string, request, jsonify, send_from_directory
import json

app = Flask(__name__)

# Store deployment status
deployment_status = {}

# HTML template for the form
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Fully Automated VM Deployment</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            border-radius: 10px;
            color: white;
        }
        .features {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        input, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 30px;
            justify-content: center;
        }
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-success {
            background: #4CAF50;
            color: white;
        }
        .btn-success:hover {
            background: #45a049;
        }
        .btn-danger {
            background: #f44336;
            color: white;
        }
        .status-container {
            margin-top: 30px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }
        .status-success {
            background: #e8f5e8;
            border: 2px solid #4CAF50;
            color: #2e7d32;
        }
        .status-error {
            background: #ffebee;
            border: 2px solid #f44336;
            color: #c62828;
        }
        .status-info {
            background: #e3f2fd;
            border: 2px solid #2196F3;
            color: #1565c0;
        }
        .logs {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Fully Automated VM Deployment</h1>
            <p>Fill the form → Click Deploy → VM is created automatically!</p>
        </div>

        <div class="features">
            <strong>✨ Fully Automated Deployment</strong>
            <ul>
                <li>✅ No manual script execution required</li>
                <li>✅ No file downloads needed</li>
                <li>✅ Scripts are generated and executed automatically</li>
                <li>✅ Real-time deployment progress</li>
                <li>✅ One-click deployment process</li>
            </ul>
        </div>

        <form id="vmForm">
            <div class="form-group">
                <label for="vmName">VM Name *</label>
                <input type="text" id="vmName" name="vmName" value="CCH9APPVMCT" required>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="vmCount">VM Count</label>
                    <input type="number" id="vmCount" name="vmCount" value="1" min="1" max="10">
                </div>
                <div class="form-group">
                    <label for="vmSize">VM Size</label>
                    <select id="vmSize" name="vmSize">
                        <option value="Standard_D4s_v3" selected>Standard_D4s_v3 (4 vCPUs, 16GB RAM)</option>
                        <option value="Standard_D2s_v3">Standard_D2s_v3 (2 vCPUs, 8GB RAM)</option>
                        <option value="Standard_D8s_v3">Standard_D8s_v3 (8 vCPUs, 32GB RAM)</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="resourceGroup">Resource Group</label>
                    <input type="text" id="resourceGroup" name="resourceGroup" value="MAH9-SUP-CCH9-VMC">
                </div>
                <div class="form-group">
                    <label for="location">Location</label>
                    <select id="location" name="location">
                        <option value="East US 2" selected>East US 2</option>
                        <option value="West US 2">West US 2</option>
                        <option value="Central US">Central US</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="adminUsername">Admin Username</label>
                    <input type="text" id="adminUsername" name="adminUsername" value="maoperator">
                </div>
                <div class="form-group">
                    <label for="adminPassword">Admin Password</label>
                    <input type="password" id="adminPassword" name="adminPassword" value="M1t1g@t0r2025">
                </div>
            </div>

            <div class="form-group">
                <label for="teamProject">Team Project</label>
                <input type="text" id="teamProject" name="teamProject" value="MAH9-SCP-CCH9GI">
            </div>

            <div class="form-group">
                <label for="deploymentGroup">Deployment Group</label>
                <input type="text" id="deploymentGroup" name="deploymentGroup" value="MAH9-SCP-CCH9-DGR-DEV">
            </div>

            <div class="form-group">
                <label for="azureDevOpsPat">Azure DevOps PAT</label>
                <input type="password" id="azureDevOpsPat" name="azureDevOpsPat" value="4znAQCp60VTfBLNJ1BRLFtP9S0dC7a9GJAsDHgGWbvqHMzLTZacPJQQJ99BGACAAAAA71N4WAAASAZDO2Yr7">
            </div>

            <div class="button-group">
                <button type="button" class="btn btn-success" onclick="deployVM('apply')">🚀 Deploy VM Now</button>
                <button type="button" class="btn btn-danger" onclick="deployVM('destroy')">🗑️ Destroy Infrastructure</button>
            </div>
        </form>

        <div id="statusContainer" class="status-container">
            <div id="statusMessage"></div>
            <div id="logs" class="logs" style="display: none;"></div>
        </div>
    </div>

    <script>
        let deploymentId = null;
        let statusInterval = null;

        function deployVM(action) {
            const form = document.getElementById('vmForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            if (action === 'destroy' && !confirm('⚠️ WARNING: This will destroy all infrastructure. Are you sure?')) {
                return;
            }

            if (action === 'apply' && !confirm('This will create real Azure resources and may incur costs. Continue?')) {
                return;
            }

            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            data.action = action;

            // Start deployment
            fetch('/deploy', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    deploymentId = result.deploymentId;
                    showStatus('info', `🚀 Deployment started for ${data.vmName}...`);
                    startStatusPolling();
                } else {
                    showStatus('error', `❌ Failed to start deployment: ${result.message}`);
                }
            })
            .catch(error => {
                showStatus('error', `❌ Error: ${error.message}`);
            });
        }

        function startStatusPolling() {
            statusInterval = setInterval(() => {
                if (deploymentId) {
                    fetch(`/status/${deploymentId}`)
                        .then(response => response.json())
                        .then(status => {
                            updateStatus(status);
                            if (status.status === 'completed' || status.status === 'failed') {
                                clearInterval(statusInterval);
                            }
                        });
                }
            }, 2000);
        }

        function updateStatus(status) {
            const statusType = status.status === 'failed' ? 'error' : 
                              status.status === 'completed' ? 'success' : 'info';
            
            showStatus(statusType, status.message);
            
            if (status.logs && status.logs.length > 0) {
                const logsDiv = document.getElementById('logs');
                logsDiv.textContent = status.logs.join('\\n');
                logsDiv.style.display = 'block';
            }
        }

        function showStatus(type, message) {
            const container = document.getElementById('statusContainer');
            const messageDiv = document.getElementById('statusMessage');
            
            container.className = `status-container status-${type}`;
            container.style.display = 'block';
            messageDiv.textContent = message;
        }
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/deploy', methods=['POST'])
def deploy():
    data = request.json
    deployment_id = str(int(time.time()))
    
    # Initialize deployment status
    deployment_status[deployment_id] = {
        'status': 'starting',
        'message': f'Initializing {data["action"]} for {data["vmName"]}...',
        'logs': [],
        'start_time': datetime.now().isoformat()
    }
    
    # Start deployment in background thread
    thread = threading.Thread(target=execute_deployment, args=(deployment_id, data))
    thread.daemon = True
    thread.start()
    
    return jsonify({
        'success': True,
        'deploymentId': deployment_id,
        'message': 'Deployment started'
    })

@app.route('/status/<deployment_id>')
def get_status(deployment_id):
    status = deployment_status.get(deployment_id, {'status': 'not_found', 'message': 'Deployment not found'})
    return jsonify(status)

def execute_deployment(deployment_id, data):
    try:
        # Update status
        deployment_status[deployment_id]['status'] = 'running'
        deployment_status[deployment_id]['message'] = f'Creating terraform.tfvars for {data["vmName"]}...'
        
        # Create terraform.tfvars
        tfvars_content = f'''vm_name = "{data['vmName']}"
vm_count = {data['vmCount']}
vm_resourcegroup = "{data['resourceGroup']}"
vm_size = "{data['vmSize']}"
vm_sku = "2022-datacenter"
vm_location = "{data['location']}"
vm_admin_username = "{data['adminUsername']}"
vm_admin_password = "{data['adminPassword']}"
azure_devops_organization = "https://dev.azure.com/MAHealth"
azure_devops_teamproject = "{data['teamProject']}"
azure_devops_deploymentgroup = "{data['deploymentGroup']}"
azure_devops_pat = "{data['azureDevOpsPat']}"
azure_devops_agentfolder = "c:/Agent"
'''
        
        with open('terraform.tfvars', 'w') as f:
            f.write(tfvars_content)
        
        deployment_status[deployment_id]['logs'].append('✅ terraform.tfvars created')
        
        # Run terraform init
        deployment_status[deployment_id]['message'] = 'Initializing Terraform...'
        run_command(['terraform', 'init'], deployment_id)
        
        # Run terraform validate
        deployment_status[deployment_id]['message'] = 'Validating configuration...'
        run_command(['terraform', 'validate'], deployment_id)
        
        # Run terraform action
        if data['action'] == 'apply':
            deployment_status[deployment_id]['message'] = f'Deploying VM {data["vmName"]}... (this may take 10-15 minutes)'
            run_command(['terraform', 'apply', '-var-file=terraform.tfvars', '-auto-approve'], deployment_id)
            deployment_status[deployment_id]['status'] = 'completed'
            deployment_status[deployment_id]['message'] = f'✅ VM {data["vmName"]} deployed successfully!'
        elif data['action'] == 'destroy':
            deployment_status[deployment_id]['message'] = 'Destroying infrastructure...'
            run_command(['terraform', 'destroy', '-var-file=terraform.tfvars', '-auto-approve'], deployment_id)
            deployment_status[deployment_id]['status'] = 'completed'
            deployment_status[deployment_id]['message'] = '✅ Infrastructure destroyed successfully!'
            
    except Exception as e:
        deployment_status[deployment_id]['status'] = 'failed'
        deployment_status[deployment_id]['message'] = f'❌ Deployment failed: {str(e)}'
        deployment_status[deployment_id]['logs'].append(f'ERROR: {str(e)}')

def run_command(cmd, deployment_id):
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        deployment_status[deployment_id]['logs'].append(f'Command: {" ".join(cmd)}')
        if result.stdout:
            deployment_status[deployment_id]['logs'].append(f'Output: {result.stdout}')
        return result
    except subprocess.CalledProcessError as e:
        deployment_status[deployment_id]['logs'].append(f'Command failed: {" ".join(cmd)}')
        deployment_status[deployment_id]['logs'].append(f'Error: {e.stderr}')
        raise e

if __name__ == '__main__':
    print("🚀 Starting Terraform Web Application...")
    print("📁 Working directory:", os.getcwd())
    print("⚡ Open your browser and go to: http://localhost:5000")
    print("")
    app.run(debug=True, host='0.0.0.0', port=5000)
