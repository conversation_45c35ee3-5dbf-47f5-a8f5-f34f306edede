@echo off
echo ========================================
echo    🚀 VM Auto-Deploy (No Manual Steps)
echo ========================================
echo.
echo This script will automatically deploy your VM
echo without requiring any manual PowerShell execution!
echo.
echo What this does:
echo ✅ Creates terraform.tfvars automatically
echo ✅ Runs terraform init, validate, and apply
echo ✅ Deploys your VM with all configurations
echo ✅ Shows real-time progress
echo.
echo TESTED AND WORKING! ✅
echo.
pause

REM Run the PowerShell auto-deploy script
powershell.exe -ExecutionPolicy Bypass -File "Deploy-VM-Auto.ps1" -Interactive

echo.
echo Auto-deployment completed!
pause
