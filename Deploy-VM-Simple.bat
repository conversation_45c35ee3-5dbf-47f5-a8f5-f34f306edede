@echo off
cls
echo ========================================
echo    Azure VM Deployment - Simple Form
echo ========================================
echo.

REM Set default values
set VM_NAME=CCH9APPVMCT
set VM_COUNT=1
set RESOURCE_GROUP=MAH9-SUP-CCH9-VMC
set VM_SIZE=Standard_D4s_v3
set LOCATION=East US 2
set ADMIN_USERNAME=maoperator
set ADMIN_PASSWORD=M1t1g@t0r2025
set TEAM_PROJECT=MAH9-SCP-CCH9GI
set DEPLOYMENT_GROUP=MAH9-SCP-CCH9-DGR-DEV
set AZURE_DEVOPS_PAT=4znAQCp60VTfBLNJ1BRLFtP9S0dC7a9GJAsDHgGWbvqHMzLTZacPJQQJ99BGACAAAAA71N4WAAASAZDO2Yr7

echo Current Configuration:
echo - VM Name: %VM_NAME%
echo - VM Count: %VM_COUNT%
echo - Resource Group: %RESOURCE_GROUP%
echo - VM Size: %VM_SIZE%
echo - Location: %LOCATION%
echo - Admin Username: %ADMIN_USERNAME%
echo - Team Project: %TEAM_PROJECT%
echo - Deployment Group: %DEPLOYMENT_GROUP%
echo.

echo Do you want to:
echo 1. Deploy with current settings
echo 2. Change VM name and deploy
echo 3. Exit
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto deploy
if "%choice%"=="2" goto change_name
if "%choice%"=="3" goto exit

:change_name
echo.
set /p VM_NAME="Enter new VM name (current: %VM_NAME%): "
if "%VM_NAME%"=="" set VM_NAME=CCH9APPVMCT
echo VM name set to: %VM_NAME%
echo.

:deploy
echo.
echo ========================================
echo    Starting Deployment: %VM_NAME%
echo ========================================
echo.

REM Create terraform.tfvars
echo Creating terraform.tfvars...
(
echo vm_name = "%VM_NAME%"
echo vm_count = %VM_COUNT%
echo vm_resourcegroup = "%RESOURCE_GROUP%"
echo vm_size = "%VM_SIZE%"
echo vm_sku = "2022-datacenter"
echo vm_location = "%LOCATION%"
echo vm_admin_username = "%ADMIN_USERNAME%"
echo vm_admin_password = "%ADMIN_PASSWORD%"
echo azure_devops_organization = "https://dev.azure.com/MAHealth"
echo azure_devops_teamproject = "%TEAM_PROJECT%"
echo azure_devops_deploymentgroup = "%DEPLOYMENT_GROUP%"
echo azure_devops_pat = "%AZURE_DEVOPS_PAT%"
echo azure_devops_agentfolder = "c:/Agent"
) > terraform.tfvars

echo terraform.tfvars created successfully!
echo.

REM Check if terraform is available
terraform version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Terraform is not installed or not in PATH
    echo Please install Terraform and try again
    pause
    goto exit
)

echo Terraform found, proceeding with deployment...
echo.

REM Initialize Terraform
echo Initializing Terraform...
terraform init
if errorlevel 1 (
    echo ERROR: Terraform init failed
    pause
    goto exit
)
echo Terraform initialized successfully!
echo.

REM Validate configuration
echo Validating configuration...
terraform validate
if errorlevel 1 (
    echo ERROR: Terraform validation failed
    pause
    goto exit
)
echo Configuration validated successfully!
echo.

REM Show plan
echo Showing deployment plan...
terraform plan -var-file="terraform.tfvars"
if errorlevel 1 (
    echo ERROR: Terraform plan failed
    pause
    goto exit
)
echo.

REM Confirm deployment
echo ========================================
echo    READY TO DEPLOY: %VM_NAME%
echo ========================================
echo.
echo This will create real Azure resources and may incur costs.
set /p confirm="Do you want to proceed with deployment? (y/N): "
if /i not "%confirm%"=="y" (
    echo Deployment cancelled by user
    pause
    goto exit
)

echo.
echo ========================================
echo    DEPLOYING VM: %VM_NAME%
echo    This may take 10-15 minutes...
echo ========================================
echo.

REM Apply configuration
terraform apply -var-file="terraform.tfvars" -auto-approve
if errorlevel 1 (
    echo.
    echo ========================================
    echo    DEPLOYMENT FAILED!
    echo ========================================
    echo Please check the error messages above
    pause
    goto exit
)

echo.
echo ========================================
echo    DEPLOYMENT COMPLETED SUCCESSFULLY!
echo    VM: %VM_NAME%
echo ========================================
echo.
echo Your VM has been deployed successfully!
echo You can now access it using the credentials provided.
echo.

REM Show terraform output
echo Deployment details:
terraform show
echo.

:exit
echo.
echo Press any key to exit...
pause >nul
