# Power Automate Terraform Deployment Setup Guide

## Overview
This guide explains how to deploy your Terraform configuration using Power Automate. There are two approaches provided:

1. **Container-based approach** - Uses Azure Container Instances to run Terraform
2. **PowerShell-based approach** - Uses PowerShell script execution

## Prerequisites

### 1. Azure Service Principal
Create a service principal for Terraform authentication:

```bash
az ad sp create-for-rbac --name "terraform-sp" --role="Contributor" --scopes="/subscriptions/YOUR_SUBSCRIPTION_ID"
```

Save the output values:
- `appId` (Client ID)
- `password` (Client Secret)
- `tenant` (Tenant ID)

### 2. Storage Account for Terraform State
Create a storage account to store Terraform state:

```bash
# Create resource group
az group create --name terraform-state-rg --location "East US 2"

# Create storage account
az storage account create \
  --resource-group terraform-state-rg \
  --name terraformstatestorage \
  --sku Standard_LRS \
  --encryption-services blob

# Create container
az storage container create \
  --name tfstate \
  --account-name terraformstatestorage
```

### 3. Fix VM Extension Error
The VM extension error has been fixed in the updated `main.tf` file with these changes:

- Updated to use `protected_settings` instead of `settings` for sensitive data
- Added proper JSON encoding
- Updated type handler version to 1.10
- Added explicit dependency
- Added sleep delay before chocolatey installation
- Fixed syntax for modern Terraform (removed deprecated interpolation)

## Power Automate Setup

### Option 1: Container-based Deployment

1. **Import the Flow**
   - Use the `power-automate-terraform-deployment.json` file
   - Import it into Power Automate

2. **Configure Connections**
   - Azure Container Instance connection
   - Microsoft Teams connection (for notifications)

3. **Update Parameters**
   - Replace placeholder values with your actual values
   - Set your subscription ID, resource group names, etc.

4. **Configure Authentication**
   - Add the service principal credentials as secure parameters:
     - `ARM_CLIENT_ID`
     - `ARM_CLIENT_SECRET`
     - `ARM_TENANT_ID`

### Option 2: PowerShell-based Deployment

1. **Create a new Power Automate Flow**

2. **Add HTTP Trigger**
   ```json
   {
     "type": "object",
     "properties": {
       "action": {
         "type": "string",
         "enum": ["plan", "apply", "destroy"]
       },
       "vmCount": {
         "type": "integer",
         "default": 1
       },
       "vmName": {
         "type": "string",
         "default": "CCH9APPVMCT"
       }
     },
     "required": ["action"]
   }
   ```

3. **Add PowerShell Script Action**
   - Use Azure Automation or Azure Functions
   - Upload the `Deploy-Terraform-PowerAutomate.ps1` script

## Usage

### Triggering the Flow

Send a POST request to your Power Automate trigger URL:

```json
{
  "action": "plan",
  "vmCount": 2,
  "vmName": "MyTestVM"
}
```

### Available Actions

- **plan**: Generate and show an execution plan
- **apply**: Build or change infrastructure
- **destroy**: Destroy Terraform-managed infrastructure

## Security Considerations

1. **Store secrets securely**
   - Use Azure Key Vault for sensitive values
   - Never hardcode credentials in the flow

2. **Limit access**
   - Restrict who can trigger the flow
   - Use appropriate RBAC permissions

3. **Audit logging**
   - Enable logging for all actions
   - Monitor Terraform state changes

## Troubleshooting

### Common Issues

1. **VM Extension Fails**
   - Check the updated `main.tf` file
   - Ensure proper PowerShell execution policy
   - Verify Azure DevOps PAT token is valid

2. **Authentication Errors**
   - Verify service principal permissions
   - Check subscription ID and tenant ID

3. **State File Issues**
   - Ensure storage account exists and is accessible
   - Check container permissions

### Debugging Steps

1. **Check Container Logs** (Container approach)
   - Review the container execution logs
   - Look for Terraform error messages

2. **PowerShell Execution** (PowerShell approach)
   - Check PowerShell execution logs
   - Verify script parameters

3. **Terraform State**
   - Check if state file is being created/updated
   - Verify backend configuration

## Best Practices

1. **Use separate environments**
   - Different flows for dev/staging/prod
   - Separate state files per environment

2. **Implement approval workflows**
   - Add approval steps for production deployments
   - Require manual approval for destroy operations

3. **Monitor and alert**
   - Set up notifications for failed deployments
   - Monitor resource costs and usage

4. **Version control**
   - Keep Terraform files in source control
   - Tag releases for tracking

## Next Steps

1. Test the flow with a plan operation first
2. Gradually roll out to different environments
3. Add additional validation and approval steps
4. Implement automated testing of infrastructure

## Support

For issues with:
- **Terraform**: Check Terraform documentation and logs
- **Power Automate**: Review flow execution history
- **Azure Resources**: Check Azure portal and activity logs
