# Automated VM Deployment System
# This script creates a fully automated deployment system without manual intervention
# Author: Auto-Generated
# Date: 2025-07-08

param(
    [string]$Port = "8080"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    🚀 Automated VM Deployment System" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to create deployment scripts automatically
function New-DeploymentScript {
    param(
        [string]$VmName,
        [string]$Action,
        [hashtable]$Parameters
    )
    
    $scriptName = "deploy-$Action-$VmName"
    
    # Create terraform.tfvars
    $tfvarsContent = @"
vm_name = "$($Parameters.vmName)"
vm_count = $($Parameters.vmCount)
vm_resourcegroup = "$($Parameters.resourceGroup)"
vm_size = "$($Parameters.vmSize)"
vm_sku = "$($Parameters.vmSku)"
vm_location = "$($Parameters.location)"
vm_admin_username = "$($Parameters.adminUsername)"
vm_admin_password = "$($Parameters.adminPassword)"
azure_devops_organization = "$($Parameters.azureDevOpsOrg)"
azure_devops_teamproject = "$($Parameters.teamProject)"
azure_devops_deploymentgroup = "$($Parameters.deploymentGroup)"
azure_devops_pat = "$($Parameters.azureDevOpsPat)"
azure_devops_agentfolder = "$($Parameters.agentFolder)"
"@
    
    $tfvarsContent | Out-File -FilePath "terraform.tfvars" -Encoding UTF8
    
    # Create PowerShell script
    $psContent = @"
# Auto-Generated Deployment Script
# VM: $VmName
# Action: $Action
# Generated: $(Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ")

param(
    [string]`$TerraformDir = "$PWD"
)

Clear-Host
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Auto-Deploy: $VmName" -ForegroundColor Cyan
Write-Host "    Action: $($Action.ToUpper())" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Change to Terraform directory
if (Test-Path `$TerraformDir) {
    Set-Location `$TerraformDir
    Write-Host "Changed to Terraform directory" -ForegroundColor Green
} else {
    Write-Host "Terraform directory not found: `$TerraformDir" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check prerequisites
if (!(Test-Path "main.tf")) {
    Write-Host "main.tf not found in current directory" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (!(Get-Command terraform -ErrorAction SilentlyContinue)) {
    Write-Host "Terraform not found in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Prerequisites checked" -ForegroundColor Green
Write-Host ""

try {
    # Initialize Terraform
    Write-Host "Initializing Terraform..." -ForegroundColor Cyan
    terraform init
    if (`$LASTEXITCODE -ne 0) { throw "Terraform init failed" }
    Write-Host "Terraform initialized" -ForegroundColor Green

    # Validate
    Write-Host "Validating configuration..." -ForegroundColor Cyan
    terraform validate
    if (`$LASTEXITCODE -ne 0) { throw "Terraform validation failed" }
    Write-Host "Configuration validated" -ForegroundColor Green

    # Execute action
    Write-Host "Running terraform $Action..." -ForegroundColor Cyan
    
    if ("$Action" -eq "plan") {
        terraform plan -var-file="terraform.tfvars"
        if (`$LASTEXITCODE -ne 0) { throw "Terraform plan failed" }
    } elseif ("$Action" -eq "apply") {
        Write-Host "This may take 10-15 minutes..." -ForegroundColor Yellow
        terraform apply -var-file="terraform.tfvars" -auto-approve
        if (`$LASTEXITCODE -ne 0) { throw "Terraform apply failed" }
    } elseif ("$Action" -eq "destroy") {
        terraform destroy -var-file="terraform.tfvars" -auto-approve
        if (`$LASTEXITCODE -ne 0) { throw "Terraform destroy failed" }
    }

    Write-Host ""
    Write-Host "Terraform $Action completed successfully!" -ForegroundColor Green

} catch {
    Write-Host ""
    Write-Host "Deployment failed!" -ForegroundColor Red
    Write-Host "Error: `$(`$_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Process completed" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Read-Host "Press Enter to exit"
"@
    
    $psContent | Out-File -FilePath "$scriptName.ps1" -Encoding UTF8
    
    # Create batch file
    $batContent = @"
@echo off
echo ========================================
echo    Auto-Deploy: $VmName
echo    Action: $($Action.ToUpper())
echo ========================================
echo.
echo This will automatically $Action your VM!
echo.
pause

REM Run the PowerShell deployment script
powershell.exe -ExecutionPolicy Bypass -File "$scriptName.ps1"

echo.
echo Deployment process completed!
pause
"@
    
    $batContent | Out-File -FilePath "$scriptName.bat" -Encoding ASCII
    
    # Unblock files
    Unblock-File -Path "$scriptName.ps1" -ErrorAction SilentlyContinue
    Unblock-File -Path "$scriptName.bat" -ErrorAction SilentlyContinue
    Unblock-File -Path "terraform.tfvars" -ErrorAction SilentlyContinue
    
    return @{
        PowerShellScript = "$scriptName.ps1"
        BatchFile = "$scriptName.bat"
        TerraformVars = "terraform.tfvars"
    }
}

# Function to execute deployment automatically
function Invoke-AutoDeployment {
    param(
        [hashtable]$DeploymentParams,
        [string]$Action = "apply"
    )
    
    Write-Host "Creating deployment scripts for $($DeploymentParams.vmName)..." -ForegroundColor Cyan
    
    $scripts = New-DeploymentScript -VmName $DeploymentParams.vmName -Action $Action -Parameters $DeploymentParams
    
    Write-Host "Scripts created successfully:" -ForegroundColor Green
    Write-Host "  - $($scripts.PowerShellScript)" -ForegroundColor White
    Write-Host "  - $($scripts.BatchFile)" -ForegroundColor White
    Write-Host "  - $($scripts.TerraformVars)" -ForegroundColor White
    Write-Host ""
    
    # Auto-execute the deployment
    Write-Host "Auto-executing deployment..." -ForegroundColor Yellow
    & ".\$($scripts.PowerShellScript)"
}

Write-Host "Automated Deployment System Ready!" -ForegroundColor Green
Write-Host ""
Write-Host "Usage Examples:" -ForegroundColor Cyan
Write-Host "1. Create and run deployment automatically:" -ForegroundColor White
Write-Host '   $params = @{' -ForegroundColor Gray
Write-Host '       vmName = "TESTVM01"' -ForegroundColor Gray
Write-Host '       vmCount = 1' -ForegroundColor Gray
Write-Host '       resourceGroup = "MAH9-SUP-CCH9-VMC"' -ForegroundColor Gray
Write-Host '       vmSize = "Standard_D4s_v3"' -ForegroundColor Gray
Write-Host '       vmSku = "2022-datacenter"' -ForegroundColor Gray
Write-Host '       location = "East US 2"' -ForegroundColor Gray
Write-Host '       adminUsername = "maoperator"' -ForegroundColor Gray
Write-Host '       adminPassword = "M1t1g@t0r2025"' -ForegroundColor Gray
Write-Host '       azureDevOpsOrg = "https://dev.azure.com/MAHealth"' -ForegroundColor Gray
Write-Host '       teamProject = "MAH9-SCP-CCH9GI"' -ForegroundColor Gray
Write-Host '       deploymentGroup = "MAH9-SCP-CCH9-DGR-DEV"' -ForegroundColor Gray
Write-Host '       azureDevOpsPat = "YOUR_PAT_TOKEN"' -ForegroundColor Gray
Write-Host '       agentFolder = "c:/Agent"' -ForegroundColor Gray
Write-Host '   }' -ForegroundColor Gray
Write-Host '   Invoke-AutoDeployment -DeploymentParams $params -Action "apply"' -ForegroundColor Gray
Write-Host ""
Write-Host "2. Just create scripts (no auto-execution):" -ForegroundColor White
Write-Host '   New-DeploymentScript -VmName "TESTVM01" -Action "plan" -Parameters $params' -ForegroundColor Gray
Write-Host ""
Write-Host "Ready for automated deployments! 🚀" -ForegroundColor Green
