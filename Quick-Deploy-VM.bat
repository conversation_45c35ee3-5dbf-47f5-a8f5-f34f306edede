@echo off
echo ========================================
echo    Azure VM Quick Deployment Tool
echo ========================================
echo.

REM Check if we're in the right directory
if not exist "main.tf" (
    echo ERROR: main.tf not found. Please run this script from your Terraform directory.
    echo Current directory: %CD%
    echo Expected directory: D:\Maspects\MAHealth\Terraform\Scope_Demo-Environment
    echo.
    pause
    exit /b 1
)

echo Current directory: %CD%
echo.

REM Get user input
set /p VM_NAME="Enter VM Name (default: CCH9APPVMCT): "
if "%VM_NAME%"=="" set VM_NAME=CCH9APPVMCT

set /p VM_COUNT="Enter VM Count (default: 1): "
if "%VM_COUNT%"=="" set VM_COUNT=1

set /p ACTION="Enter action (plan/apply/destroy) [default: plan]: "
if "%ACTION%"=="" set ACTION=plan

echo.
echo ========================================
echo Configuration Summary:
echo ========================================
echo VM Name: %VM_NAME%
echo VM Count: %VM_COUNT%
echo Action: %ACTION%
echo Resource Group: MAH9-SUP-CCH9-VMC
echo ========================================
echo.

if "%ACTION%"=="apply" (
    echo WARNING: This will create real Azure resources and may incur costs!
    set /p CONFIRM="Are you sure you want to proceed? (yes/no): "
    if not "!CONFIRM!"=="yes" (
        echo Deployment cancelled.
        pause
        exit /b 0
    )
)

if "%ACTION%"=="destroy" (
    echo WARNING: This will destroy all infrastructure!
    set /p CONFIRM="Are you sure you want to destroy resources? (yes/no): "
    if not "!CONFIRM!"=="yes" (
        echo Destroy cancelled.
        pause
        exit /b 0
    )
)

echo.
echo Starting deployment...
echo.

REM Execute the PowerShell script
powershell.exe -ExecutionPolicy Bypass -Command "& {.\Deploy-VM-Terraform.ps1 -Action '%ACTION%' -VmName '%VM_NAME%' -VmCount %VM_COUNT% -ResourceGroupName 'MAH9-SUP-CCH9-VMC' -VmSize 'Standard_D4s_v3' -VmSku '2022-datacenter' -Location 'East US 2' -AdminUsername 'maoperator' -AdminPassword 'M1t1g@t0r2025' -AzureDevOpsOrg 'https://dev.azure.com/MAHealth' -TeamProject 'MAH9-SCP-CCH9GI' -DeploymentGroup 'MAH9-SCP-CCH9-DGR-DEV' -AzureDevOpsPat '4znAQCp60VTfBLNJ1BRLFtP9S0dC7a9GJAsDHgGWbvqHMzLTZacPJQQJ99BGACAAAAA71N4WAAASAZDO2Yr7' -AgentFolder 'c:/Agent' -AutoApprove}"

echo.
echo ========================================
echo Deployment completed!
echo ========================================
echo.

if "%ACTION%"=="plan" (
    echo The plan has been generated. Review the output above.
    echo If everything looks good, run this script again with 'apply' action.
)

if "%ACTION%"=="apply" (
    echo VM deployment completed! Check Azure portal to verify resources.
    echo VM Name: %VM_NAME%-1
    echo Resource Group: MAH9-SUP-CCH9-VMC
)

echo.
pause
