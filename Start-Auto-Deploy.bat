@echo off
echo ========================================
echo   Terraform Auto-Deploy (PowerShell)
echo ========================================
echo.

REM Check if we're in the right directory
if not exist "main.tf" (
    echo ERROR: main.tf not found. Please run this script from your Terraform directory.
    echo Current directory: %CD%
    echo Expected directory: D:\Maspects\MAHealth\Terraform\Scope_Demo-Environment
    echo.
    pause
    exit /b 1
)

echo ✅ Found main.tf - correct directory
echo Current directory: %CD%
echo.

REM Check if Terraform is installed
terraform --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Terraform is not installed!
    echo Please install Terraform from: https://terraform.io/downloads
    echo Or use Chocolatey: choco install terraform
    echo.
    pause
    exit /b 1
)

echo ✅ Terraform is installed
terraform --version

REM Check if Azure CLI is installed
az --version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Azure CLI is not installed!
    echo Please install Azure CLI from: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli
    echo Or use Chocolatey: choco install azure-cli
    echo.
    echo You can continue, but you'll need Azure CLI for authentication.
    pause
) else (
    echo ✅ Azure CLI is installed
)

REM Check if PowerShell web server exists
if not exist "Start-TerraformWebServer.ps1" (
    echo ERROR: Start-TerraformWebServer.ps1 not found!
    echo Please ensure all files are in the current directory.
    echo.
    pause
    exit /b 1
)

echo ✅ PowerShell web server found

REM Check if the auto-deploy form exists
if not exist "vm-deployment-form-auto.html" (
    echo ERROR: vm-deployment-form-auto.html not found!
    echo Please ensure all files are in the current directory.
    echo.
    pause
    exit /b 1
)

echo ✅ Auto-deploy form found
echo.

echo ========================================
echo Starting PowerShell Web Server...
echo ========================================
echo.
echo 🚀 Starting Terraform Auto-Deploy Server
echo 📁 Working Directory: %CD%
echo 🌐 Server will start on: http://localhost:8080
echo.
echo Once started:
echo 1. Open your browser
echo 2. Go to: http://localhost:8080
echo 3. Fill out the form
echo 4. Click "Deploy VM Now"
echo 5. Watch automatic deployment!
echo.
echo Press Ctrl+C to stop the server when done.
echo.
pause

REM Start the PowerShell web server
powershell.exe -ExecutionPolicy Bypass -File "Start-TerraformWebServer.ps1" -Port 8080

echo.
echo ========================================
echo Server stopped
echo ========================================
pause
