{"definition": {"$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#", "contentVersion": "*******", "parameters": {"resourceGroupName": {"type": "string", "defaultValue": "MAH9-SUP-CCH9-VMC"}, "subscriptionId": {"type": "string", "defaultValue": "your-subscription-id"}, "storageAccountName": {"type": "string", "defaultValue": "terraformstatestorage"}, "containerName": {"type": "string", "defaultValue": "tfstate"}}, "triggers": {"manual": {"type": "Request", "kind": "Http", "inputs": {"schema": {"type": "object", "properties": {"action": {"type": "string", "enum": ["plan", "apply", "destroy"]}, "vmCount": {"type": "integer", "default": 1}, "vmName": {"type": "string", "default": "CCH9APPVMCT"}}, "required": ["action"]}}}}, "actions": {"Initialize_Variables": {"type": "InitializeVariable", "inputs": {"variables": [{"name": "terraformAction", "type": "string", "value": "@triggerBody()?['action']"}, {"name": "vmCount", "type": "integer", "value": "@triggerBody()?['vmCount']"}, {"name": "vmName", "type": "string", "value": "@triggerBody()?['vmName']"}]}, "runAfter": {}}, "Create_Container_Instance": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['azurecontainerinstance']['connectionId']"}}, "method": "put", "path": "/subscriptions/@{parameters('subscriptionId')}/resourceGroups/@{parameters('resourceGroupName')}/providers/Microsoft.ContainerInstance/containerGroups/terraform-runner", "queries": {"api-version": "2021-03-01"}, "body": {"location": "East US 2", "properties": {"containers": [{"name": "terraform-container", "properties": {"image": "hashicorp/terraform:latest", "command": ["/bin/sh", "-c", "apk add --no-cache git curl && git clone https://github.com/your-repo/terraform-config.git /workspace && cd /workspace && terraform init -backend-config=\"storage_account_name=@{parameters('storageAccountName')}\" -backend-config=\"container_name=@{parameters('containerName')}\" -backend-config=\"key=terraform.tfstate\" && terraform @{variables('terraformAction')} -var=\"vm_count=@{variables('vmCount')}\" -var=\"vm_name=@{variables('vmName')}\" -auto-approve"], "environmentVariables": [{"name": "ARM_CLIENT_ID", "secureValue": "@parameters('ARM_CLIENT_ID')"}, {"name": "ARM_CLIENT_SECRET", "secureValue": "@parameters('ARM_CLIENT_SECRET')"}, {"name": "ARM_SUBSCRIPTION_ID", "value": "@parameters('subscriptionId')"}, {"name": "ARM_TENANT_ID", "secureValue": "@parameters('ARM_TENANT_ID')"}], "resources": {"requests": {"cpu": 1, "memoryInGB": 2}}}}], "osType": "Linux", "restartPolicy": "Never"}}}, "runAfter": {"Initialize_Variables": ["Succeeded"]}}, "Wait_for_Container_Completion": {"type": "Until", "expression": "@or(equals(body('Get_Container_Status')?['properties']?['instanceView']?['state'], 'Succeeded'), equals(body('Get_Container_Status')?['properties']?['instanceView']?['state'], 'Failed'))", "limit": {"count": 60, "timeout": "PT1H"}, "actions": {"Get_Container_Status": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['azurecontainerinstance']['connectionId']"}}, "method": "get", "path": "/subscriptions/@{parameters('subscriptionId')}/resourceGroups/@{parameters('resourceGroupName')}/providers/Microsoft.ContainerInstance/containerGroups/terraform-runner", "queries": {"api-version": "2021-03-01"}}}, "Delay": {"type": "Wait", "inputs": {"interval": {"count": 30, "unit": "Second"}}, "runAfter": {"Get_Container_Status": ["Succeeded"]}}}, "runAfter": {"Create_Container_Instance": ["Succeeded"]}}, "Get_Container_Logs": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['azurecontainerinstance']['connectionId']"}}, "method": "get", "path": "/subscriptions/@{parameters('subscriptionId')}/resourceGroups/@{parameters('resourceGroupName')}/providers/Microsoft.ContainerInstance/containerGroups/terraform-runner/containers/terraform-container/logs", "queries": {"api-version": "2021-03-01"}}, "runAfter": {"Wait_for_Container_Completion": ["Succeeded"]}}, "Send_Notification": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['teams']['connectionId']"}}, "method": "post", "path": "/flowbot/actions/adaptivecard/recipienttype/channel", "queries": {"recipient": "your-teams-channel-id"}, "body": {"messageBody": {"type": "AdaptiveCard", "body": [{"type": "TextBlock", "text": "Terraform Deployment @{variables('terraformAction')}", "weight": "Bolder", "size": "Medium"}, {"type": "TextBlock", "text": "Status: @{if(equals(body('Get_Container_Status')?['properties']?['instanceView']?['state'], 'Succeeded'), 'Success', 'Failed')}", "color": "@{if(equals(body('Get_Container_Status')?['properties']?['instanceView']?['state'], 'Succeeded'), 'Good', 'Attention')}"}, {"type": "TextBlock", "text": "VM Name: @{variables('vmName')}", "wrap": true}, {"type": "TextBlock", "text": "VM Count: @{variables('vmCount')}", "wrap": true}, {"type": "TextBlock", "text": "Logs:", "weight": "Bolder"}, {"type": "TextBlock", "text": "@{body('Get_Container_Logs')?['content']}", "wrap": true, "fontType": "Monospace"}], "$schema": "http://adaptivecards.io/schemas/adaptive-card.json", "version": "1.2"}}}, "runAfter": {"Get_Container_Logs": ["Succeeded"]}}, "Cleanup_Container": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['azurecontainerinstance']['connectionId']"}}, "method": "delete", "path": "/subscriptions/@{parameters('subscriptionId')}/resourceGroups/@{parameters('resourceGroupName')}/providers/Microsoft.ContainerInstance/containerGroups/terraform-runner", "queries": {"api-version": "2021-03-01"}}, "runAfter": {"Send_Notification": ["Succeeded", "Failed"]}}}, "outputs": {}}, "parameters": {"$connections": {"value": {"azurecontainerinstance": {"connectionId": "/subscriptions/{subscription-id}/resourceGroups/{resource-group}/providers/Microsoft.Web/connections/azurecontainerinstance", "connectionName": "azurecontainerinstance", "id": "/subscriptions/{subscription-id}/providers/Microsoft.Web/locations/{location}/managedApis/aci"}, "teams": {"connectionId": "/subscriptions/{subscription-id}/resourceGroups/{resource-group}/providers/Microsoft.Web/connections/teams", "connectionName": "teams", "id": "/subscriptions/{subscription-id}/providers/Microsoft.Web/locations/{location}/managedApis/teams"}}}}}