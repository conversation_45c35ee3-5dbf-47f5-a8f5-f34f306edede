@echo off
echo ========================================
echo   Terraform Auto-Deploy Setup
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please install Node.js from: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js is installed
node --version

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available!
    echo Please reinstall Node.js from: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo ✅ npm is available
npm --version

REM Check if Terraform is installed
terraform --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Terraform is not installed!
    echo Please install Terraform from: https://terraform.io/downloads
    echo Or use Chocolatey: choco install terraform
    echo.
    pause
    exit /b 1
)

echo ✅ Terraform is installed
terraform --version

REM Check if Azure CLI is installed
az --version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Azure CLI is not installed!
    echo Please install Azure CLI from: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli
    echo Or use Chocolatey: choco install azure-cli
    echo.
    echo You can continue setup, but you'll need Azure CLI for authentication.
    pause
) else (
    echo ✅ Azure CLI is installed
)

echo.
echo ========================================
echo Installing Node.js dependencies...
echo ========================================

REM Install npm dependencies
npm install

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies!
    echo Please check your internet connection and try again.
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Dependencies installed successfully!

REM Check if main.tf exists
if not exist "main.tf" (
    echo.
    echo WARNING: main.tf not found in current directory!
    echo Please make sure you're running this from your Terraform directory.
    echo Current directory: %CD%
    echo.
)

echo.
echo ========================================
echo Setup Complete! 🎉
echo ========================================
echo.
echo Next steps:
echo 1. Make sure you're logged into Azure: az login
echo 2. Start the server: npm start
echo 3. Open your browser to: http://localhost:3000
echo 4. Fill out the form and deploy VMs automatically!
echo.
echo Quick start commands:
echo   npm start          - Start the web server
echo   npm run dev        - Start with auto-restart (development)
echo.
echo The web interface will allow you to:
echo ✅ Plan deployments (see what will be created)
echo ✅ Deploy VMs automatically (no manual steps!)
echo ✅ Destroy infrastructure when needed
echo ✅ Monitor deployment progress in real-time
echo ✅ View detailed logs
echo.
pause
