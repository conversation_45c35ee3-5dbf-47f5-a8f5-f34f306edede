@echo off
echo ========================================
echo    🚀 Starting Automated VM Deployment System
echo ========================================
echo.
echo This will open the fully automated deployment form
echo No manual script execution required!
echo.
pause

REM Open the automated form in default browser
start vm-deployment-automated.html

echo.
echo ✅ Automated deployment form opened in your browser!
echo.
echo 📋 Instructions:
echo 1. Fill out the VM details in the form
echo 2. Click "Deploy VM Now" 
echo 3. Copy and run the generated PowerShell command
echo 4. Your VM will be deployed automatically!
echo.
echo 🚀 No more manual file downloads or script execution!
echo.
pause
