﻿# Auto-Generated Deployment Script
# VM: CCH9APPVMCT2
# Action: apply
# Generated: 2025-07-09T15:50:23.636Z

param(
    [string]$TerraformDir = "D:\Maspects\MAHealth\Terraform\Scope_Demo-Environment"
)

Clear-Host
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Auto-Deploy: CCH9APPVMCT2" -ForegroundColor Cyan
Write-Host "    Action: APPLY" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Change to Terraform directory
if (Test-Path $TerraformDir) {
    Set-Location $TerraformDir
    Write-Host "Changed to Terraform directory" -ForegroundColor Green
} else {
    Write-Host "Terraform directory not found: $TerraformDir" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check prerequisites
if (!(Test-Path "main.tf")) {
    Write-Host "main.tf not found in current directory" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (!(Get-Command terraform -ErrorAction SilentlyContinue)) {
    Write-Host "Terraform not found in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Prerequisites checked" -ForegroundColor Green
Write-Host ""

try {
    # Initialize Terraform
    Write-Host "Initializing Terraform..." -ForegroundColor Cyan
    terraform init
    if ($LASTEXITCODE -ne 0) { throw "Terraform init failed" }
    Write-Host "Terraform initialized" -ForegroundColor Green

    # Validate
    Write-Host "Validating configuration..." -ForegroundColor Cyan
    terraform validate
    if ($LASTEXITCODE -ne 0) { throw "Terraform validation failed" }
    Write-Host "Configuration validated" -ForegroundColor Green

    # Execute action
    Write-Host "Running terraform apply..." -ForegroundColor Cyan
    
    if ("apply" -eq "plan") {
        terraform plan -var-file="terraform.tfvars"
        if ($LASTEXITCODE -ne 0) { throw "Terraform plan failed" }
    } elseif ("apply" -eq "apply") {
        Write-Host "This may take 10-15 minutes..." -ForegroundColor Yellow
        terraform apply -var-file="terraform.tfvars" -auto-approve
        if ($LASTEXITCODE -ne 0) { throw "Terraform apply failed" }
    } elseif ("apply" -eq "destroy") {
        terraform destroy -var-file="terraform.tfvars" -auto-approve
        if ($LASTEXITCODE -ne 0) { throw "Terraform destroy failed" }
    }

    Write-Host ""
    Write-Host "Terraform apply completed successfully!" -ForegroundColor Green

} catch {
    Write-Host ""
    Write-Host "Deployment failed!" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Process completed" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Read-Host "Press Enter to exit"
