{"definition": {"$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#", "contentVersion": "*******", "parameters": {"subscriptionId": {"type": "string", "defaultValue": "41ae4571-862f-4682-ac9d-c8e39c9ce301"}, "automationAccountName": {"type": "string", "defaultValue": "terraform-automation"}, "automationResourceGroup": {"type": "string", "defaultValue": "automation-rg"}}, "triggers": {"manual": {"type": "Request", "kind": "Http", "inputs": {"schema": {"type": "object", "properties": {"action": {"type": "string", "enum": ["plan", "apply", "destroy"], "description": "Terraform action to perform"}, "vmName": {"type": "string", "description": "Base name for the VM(s)"}, "vmCount": {"type": "integer", "minimum": 1, "maximum": 10, "description": "Number of VMs to create"}, "resourceGroup": {"type": "string", "description": "Azure resource group name"}, "vmSize": {"type": "string", "description": "VM size/SKU"}, "vmSku": {"type": "string", "description": "Windows version SKU"}, "location": {"type": "string", "description": "Azure region"}, "adminUsername": {"type": "string", "description": "VM administrator username"}, "adminPassword": {"type": "string", "description": "VM administrator password"}, "azureDevOpsOrg": {"type": "string", "description": "Azure DevOps organization URL"}, "teamProject": {"type": "string", "description": "Azure DevOps team project"}, "deploymentGroup": {"type": "string", "description": "Azure DevOps deployment group"}, "azureDevOpsPat": {"type": "string", "description": "Azure DevOps Personal Access Token"}, "agentFolder": {"type": "string", "description": "Agent installation folder"}, "requestedBy": {"type": "string", "description": "Person requesting the deployment"}}, "required": ["action", "vmName", "resourceGroup", "adminUsername", "adminPassword", "azureDevOpsOrg", "teamProject", "deploymentGroup", "azureDevOpsPat"]}}}}, "actions": {"Initialize_Variables": {"type": "InitializeVariable", "inputs": {"variables": [{"name": "deploymentId", "type": "string", "value": "@{guid()}"}, {"name": "startTime", "type": "string", "value": "@{utcNow()}"}, {"name": "requestData", "type": "object", "value": "@triggerBody()"}]}, "runAfter": {}}, "Send_Initial_Notification": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['teams']['connectionId']"}}, "method": "post", "path": "/flowbot/actions/adaptivecard/recipienttype/channel", "queries": {"recipient": "your-teams-channel-id"}, "body": {"messageBody": {"type": "AdaptiveCard", "body": [{"type": "TextBlock", "text": "🚀 Terraform Deployment Started", "weight": "Bolder", "size": "Medium", "color": "Accent"}, {"type": "FactSet", "facts": [{"title": "Deployment ID:", "value": "@{variables('deploymentId')}"}, {"title": "Action:", "value": "@{variables('requestData')['action']}"}, {"title": "VM Name:", "value": "@{variables('requestData')['vmName']}"}, {"title": "VM Count:", "value": "@{variables('requestData')['vmCount']}"}, {"title": "Resource Group:", "value": "@{variables('requestData')['resourceGroup']}"}, {"title": "Requested By:", "value": "@{coalesce(variables('requestData')['requestedBy'], 'Web Form')}"}, {"title": "Started:", "value": "@{variables('startTime')}"}]}], "$schema": "http://adaptivecards.io/schemas/adaptive-card.json", "version": "1.2"}}}, "runAfter": {"Initialize_Variables": ["Succeeded"]}}, "Create_PowerShell_Script": {"type": "Compose", "inputs": {"scriptContent": "param(\n    [string]$Action = '@{variables('requestData')['action']}',\n    [string]$VmName = '@{variables('requestData')['vmName']}',\n    [int]$VmCount = @{coalesce(variables('requestData')['vmCount'], 1)},\n    [string]$ResourceGroupName = '@{variables('requestData')['resourceGroup']}',\n    [string]$VmSize = '@{coalesce(variables('requestData')['vmSize'], 'Standard_D4s_v3')}',\n    [string]$VmSku = '@{coalesce(variables('requestData')['vmSku'], '2022-datacenter-azure-edition')}',\n    [string]$Location = '@{coalesce(variables('requestData')['location'], 'East US 2')}',\n    [string]$AdminUsername = '@{variables('requestData')['adminUsername']}',\n    [string]$AdminPassword = '@{variables('requestData')['adminPassword']}',\n    [string]$AzureDevOpsOrg = '@{variables('requestData')['azureDevOpsOrg']}',\n    [string]$TeamProject = '@{variables('requestData')['teamProject']}',\n    [string]$DeploymentGroup = '@{variables('requestData')['deploymentGroup']}',\n    [string]$AzureDevOpsPat = '@{variables('requestData')['azureDevOpsPat']}',\n    [string]$AgentFolder = '@{coalesce(variables('requestData')['agentFolder'], 'c:/Agent')}'\n)\n\n# Import the deployment script\n. .\\Deploy-VM-Terraform.ps1 -Action $Action -VmName $VmName -VmCount $VmCount -ResourceGroupName $ResourceGroupName -VmSize $VmSize -VmSku $VmSku -Location $Location -AdminUsername $AdminUsername -AdminPassword $AdminPassword -AzureDevOpsOrg $AzureDevOpsOrg -TeamProject $TeamProject -DeploymentGroup $DeploymentGroup -AzureDevOpsPat $AzureDevOpsPat -AgentFolder $AgentFolder -AutoApprove"}, "runAfter": {"Send_Initial_Notification": ["Succeeded"]}}, "Run_PowerShell_Script": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['azureautomation']['connectionId']"}}, "method": "put", "path": "/subscriptions/@{parameters('subscriptionId')}/resourceGroups/@{parameters('automationResourceGroup')}/providers/Microsoft.Automation/automationAccounts/@{parameters('automationAccountName')}/jobs", "queries": {"runbookName": "Deploy-Terraform-VM", "api-version": "2020-01-13-preview"}, "body": {"properties": {"parameters": {"Action": "@{variables('requestData')['action']}", "VmName": "@{variables('requestData')['vmName']}", "VmCount": "@{coalesce(variables('requestData')['vmCount'], 1)}", "ResourceGroupName": "@{variables('requestData')['resourceGroup']}", "VmSize": "@{coalesce(variables('requestData')['vmSize'], 'Standard_D4s_v3')}", "VmSku": "@{coalesce(variables('requestData')['vmSku'], '2022-datacenter-azure-edition')}", "Location": "@{coalesce(variables('requestData')['location'], 'East US 2')}", "AdminUsername": "@{variables('requestData')['adminUsername']}", "AdminPassword": "@{variables('requestData')['adminPassword']}", "AzureDevOpsOrg": "@{variables('requestData')['azureDevOpsOrg']}", "TeamProject": "@{variables('requestData')['teamProject']}", "DeploymentGroup": "@{variables('requestData')['deploymentGroup']}", "AzureDevOpsPat": "@{variables('requestData')['azureDevOpsPat']}", "AgentFolder": "@{coalesce(variables('requestData')['agentFolder'], 'c:/Agent')}"}}}}, "runAfter": {"Create_PowerShell_Script": ["Succeeded"]}}, "Wait_for_Job_Completion": {"type": "Until", "expression": "@or(equals(body('Get_Job_Status')?['properties']?['status'], 'Completed'), equals(body('Get_Job_Status')?['properties']?['status'], 'Failed'), equals(body('Get_Job_Status')?['properties']?['status'], 'Stopped'))", "limit": {"count": 120, "timeout": "PT2H"}, "actions": {"Get_Job_Status": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['azureautomation']['connectionId']"}}, "method": "get", "path": "/subscriptions/@{parameters('subscriptionId')}/resourceGroups/@{parameters('automationResourceGroup')}/providers/Microsoft.Automation/automationAccounts/@{parameters('automationAccountName')}/jobs/@{body('Run_PowerShell_Script')?['name']}", "queries": {"api-version": "2020-01-13-preview"}}}, "Delay": {"type": "Wait", "inputs": {"interval": {"count": 1, "unit": "Minute"}}, "runAfter": {"Get_Job_Status": ["Succeeded"]}}}, "runAfter": {"Run_PowerShell_Script": ["Succeeded"]}}, "Get_Job_Output": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['azureautomation']['connectionId']"}}, "method": "get", "path": "/subscriptions/@{parameters('subscriptionId')}/resourceGroups/@{parameters('automationResourceGroup')}/providers/Microsoft.Automation/automationAccounts/@{parameters('automationAccountName')}/jobs/@{body('Run_PowerShell_Script')?['name']}/output", "queries": {"api-version": "2020-01-13-preview"}}, "runAfter": {"Wait_for_Job_Completion": ["Succeeded"]}}, "Send_Completion_Notification": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['teams']['connectionId']"}}, "method": "post", "path": "/flowbot/actions/adaptivecard/recipienttype/channel", "queries": {"recipient": "your-teams-channel-id"}, "body": {"messageBody": {"type": "AdaptiveCard", "body": [{"type": "TextBlock", "text": "@{if(equals(body('Get_Job_Status')?['properties']?['status'], 'Completed'), '✅ Terraform Deployment Completed', '❌ Terraform Deployment Failed')}", "weight": "Bolder", "size": "Medium", "color": "@{if(equals(body('Get_Job_Status')?['properties']?['status'], 'Completed'), 'Good', 'Attention')}"}, {"type": "FactSet", "facts": [{"title": "Deployment ID:", "value": "@{variables('deploymentId')}"}, {"title": "Status:", "value": "@{body('Get_Job_Status')?['properties']?['status']}"}, {"title": "Action:", "value": "@{variables('requestData')['action']}"}, {"title": "VM Name:", "value": "@{variables('requestData')['vmName']}"}, {"title": "Duration:", "value": "@{formatDateTime(utcNow(), 'yyyy-MM-dd HH:mm:ss')} (Started: @{formatDateTime(variables('startTime'), 'HH:mm:ss')})"}]}, {"type": "TextBlock", "text": "Output:", "weight": "Bolder"}, {"type": "TextBlock", "text": "@{substring(coalesce(body('Get_Job_Output'), 'No output available'), 0, min(length(coalesce(body('Get_Job_Output'), 'No output available')), 1000))}", "wrap": true, "fontType": "Monospace", "size": "Small"}], "$schema": "http://adaptivecards.io/schemas/adaptive-card.json", "version": "1.2"}}}, "runAfter": {"Get_Job_Output": ["Succeeded", "Failed"]}}, "Response": {"type": "Response", "inputs": {"statusCode": "@{if(equals(body('Get_Job_Status')?['properties']?['status'], 'Completed'), 200, 500)}", "headers": {"Content-Type": "application/json"}, "body": {"deploymentId": "@{variables('deploymentId')}", "status": "@{body('Get_Job_Status')?['properties']?['status']}", "action": "@{variables('requestData')['action']}", "vmName": "@{variables('requestData')['vmName']}", "vmCount": "@{variables('requestData')['vmCount']}", "resourceGroup": "@{variables('requestData')['resourceGroup']}", "startTime": "@{variables('startTime')}", "endTime": "@{utcNow()}", "output": "@{body('Get_Job_Output')}", "message": "@{if(equals(body('Get_Job_Status')?['properties']?['status'], 'Completed'), 'Deployment completed successfully', 'Deployment failed - check logs for details')}"}}, "runAfter": {"Send_Completion_Notification": ["Succeeded", "Failed"]}}}, "outputs": {}}, "parameters": {"$connections": {"value": {"teams": {"connectionId": "/subscriptions/{subscription-id}/resourceGroups/{resource-group}/providers/Microsoft.Web/connections/teams", "connectionName": "teams", "id": "/subscriptions/{subscription-id}/providers/Microsoft.Web/locations/{location}/managedApis/teams"}, "azureautomation": {"connectionId": "/subscriptions/{subscription-id}/resourceGroups/{resource-group}/providers/Microsoft.Web/connections/azureautomation", "connectionName": "azureautomation", "id": "/subscriptions/{subscription-id}/providers/Microsoft.Web/locations/{location}/managedApis/azureautomation"}}}}}